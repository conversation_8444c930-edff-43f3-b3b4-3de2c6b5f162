import Lenis from "lenis";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

document.addEventListener("DOMContentLoaded", () => {
  let isMobile = window.innerWidth <= 900;

  // Mobile: Disable smooth scrolling completely
  // Desktop: Keep smooth scrolling enabled
  const scrollSettings = isMobile
    ? {
        // Mobile settings - disable smooth scroll and lerp
        smooth: false,
        smoothTouch: false,
        lerp: 0, // No lerp effect on mobile
        duration: 0,
        touchMultiplier: 1,
        wheelMultiplier: 1,
        syncTouch: false,
        smoothWheel: false,
      }
    : {
        // Desktop settings - keep smooth scrolling
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        direction: "vertical",
        gestureDirection: "vertical",
        smooth: true,
        smoothTouch: false,
        touchMultiplier: 2,
        infinite: false,
        lerp: 0.1,
        wheelMultiplier: 1,
        orientation: "vertical",
        smoothWheel: true,
        syncTouch: true,
      };

  let lenis = isMobile ? null : new Lenis(scrollSettings);

  // Only initialize Lenis events and ticker if not on mobile
  if (!isMobile && lenis) {
    lenis.on("scroll", ScrollTrigger.update);

    gsap.ticker.add((time) => {
      lenis.raf(time * 1000);
    });

    gsap.ticker.lagSmoothing(0);
  } else {
    // On mobile, use native scroll for ScrollTrigger
    ScrollTrigger.refresh();
  }

  const handleResize = () => {
    const wasMobile = isMobile;
    isMobile = window.innerWidth <= 900;

    if (wasMobile !== isMobile) {
      // Destroy existing Lenis instance if it exists
      if (lenis) {
        lenis.destroy();
        lenis = null;
      }

      // Remove existing ticker if it was added
      gsap.ticker.remove((time) => {
        if (lenis) lenis.raf(time * 1000);
      });

      if (!isMobile) {
        // Switching to desktop - enable Lenis
        const desktopScrollSettings = {
          duration: 1.2,
          easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
          direction: "vertical",
          gestureDirection: "vertical",
          smooth: true,
          smoothTouch: false,
          touchMultiplier: 2,
          infinite: false,
          lerp: 0.1,
          wheelMultiplier: 1,
          orientation: "vertical",
          smoothWheel: true,
          syncTouch: true,
        };

        lenis = new Lenis(desktopScrollSettings);
        lenis.on("scroll", ScrollTrigger.update);

        gsap.ticker.add((time) => {
          lenis.raf(time * 1000);
        });
      } else {
        // Switching to mobile - use native scroll
        ScrollTrigger.refresh();
      }
    }
  };

  window.addEventListener("resize", handleResize);
});
